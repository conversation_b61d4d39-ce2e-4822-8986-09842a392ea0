/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

p {
    margin-bottom: 1rem;
    color: #666;
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-outline {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: white;
    color: #333;
    border: 2px solid #e1e5e9;
}

.btn-secondary:hover {
    border-color: #667eea;
    color: #667eea;
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.large {
    padding: 16px 32px;
    font-size: 16px;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    border-bottom: 1px solid #e1e5e9;
    transition: all 0.3s ease;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    height: 40px;
    width: auto;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #667eea;
}

.nav-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #2d3748;
}

.highlight {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #4a5568;
    line-height: 1.7;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 100%;
    transform: rotate(5deg);
    transition: transform 0.3s ease;
}

.hero-card:hover {
    transform: rotate(0deg) scale(1.05);
}

.card-header {
    margin-bottom: 1.5rem;
}

.card-stats {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.user-details h4 {
    margin-bottom: 0.5rem;
    color: #2d3748;
}

.user-details p {
    color: #666;
    font-size: 0.9rem;
}

.progress-indicators {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.progress-item {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e2e8f0;
    transition: background 0.3s ease;
}

.progress-item.active {
    background: #667eea;
}

/* What We Offer Section */
.what-we-offer {
    padding: 80px 0;
    background: #f8fafc;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    color: #2d3748;
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.1rem;
    color: #4a5568;
    max-width: 600px;
    margin: 0 auto;
}

/* Offering Blocks */
.offering-block {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 6rem;
    padding: 3rem 0;
}

.offering-block.reverse {
    direction: rtl;
}

.offering-block.reverse > * {
    direction: ltr;
}

.offering-content {
    padding: 2rem;
}

.offering-badge {
    display: inline-block;
    background: #e2e8f0;
    color: #4a5568;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
}

.offering-title {
    font-size: 2.2rem;
    color: #2d3748;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.offering-subtitle {
    font-size: 1.1rem;
    color: #4a5568;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.feature-section {
    margin-top: 2rem;
}

.feature-category {
    font-size: 0.8rem;
    color: #667eea;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.feature-title {
    font-size: 1.3rem;
    color: #2d3748;
    font-weight: 600;
    margin-bottom: 1rem;
}

.feature-description {
    color: #4a5568;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.feature-highlights {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.highlight-item {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.highlight-label {
    font-weight: 600;
    color: #2d3748;
    display: block;
    margin-bottom: 1rem;
}

.highlight-list {
    list-style: none;
    margin: 0;
}

.highlight-list li {
    padding: 0.3rem 0;
    padding-left: 1.2rem;
    position: relative;
    color: #4a5568;
    font-size: 0.9rem;
}

.highlight-list li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: #667eea;
    font-weight: bold;
}

.feature-list {
    list-style: none;
    margin-bottom: 2rem;
}

.feature-list li {
    padding: 0.7rem 0;
    padding-left: 1.5rem;
    position: relative;
    color: #4a5568;
}

.feature-list li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #667eea;
    font-weight: bold;
}

/* Visual Cards */
.offering-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.visual-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 100%;
    position: relative;
}

.card-image-placeholder {
    height: 200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.offering-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
    border-radius: 12px;
}

.visual-stats {
    text-align: center;
}

.stat-item {
    margin-bottom: 1rem;
}

.stat-time {
    font-weight: 600;
    color: #2d3748;
    display: block;
    margin-bottom: 0.5rem;
}

.time-slots {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 1rem;
}

.time-slot {
    background: #f7fafc;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

.btn-success {
    background: #48bb78;
    color: white;
    padding: 0.8rem 2rem;
    border-radius: 25px;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-success:hover {
    background: #38a169;
    transform: translateY(-2px);
}

/* Consultation Card */
.consultation-card {
    background: #f7fafc;
    padding: 1.5rem;
    border-radius: 12px;
    margin-top: 1rem;
}

.consultation-header {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 1rem;
}

.consultation-icon {
    width: 12px;
    height: 12px;
    background: #48bb78;
    border-radius: 50%;
}

.consultation-title {
    font-weight: 600;
    color: #2d3748;
}

.consultation-text {
    color: #4a5568;
    font-size: 0.9rem;
    margin: 0;
}

/* Platform Stats */
.platform-stats {
    margin-top: 1.5rem;
}

.stat-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.8rem;
}

.stat-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    font-weight: 700;
    font-size: 1.2rem;
}

/* Trust Section */
.trust-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.trust-title {
    font-size: 0.8rem;
    color: #667eea;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.trust-subtitle {
    font-size: 1.3rem;
    color: #2d3748;
    font-weight: 600;
    margin-bottom: 1rem;
}

.trust-description {
    color: #4a5568;
    margin-bottom: 2rem;
    line-height: 1.7;
}

/* Why It Matters Section */
.why-it-matters {
    padding: 80px 0;
    background: white;
}

.section-content {
    text-align: center;
}

.stakeholder-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.stakeholder-card {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.stakeholder-card h3 {
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.stakeholder-card p {
    color: #4a5568;
}

.unification-text {
    font-size: 1.2rem;
    font-weight: 500;
    color: #2d3748;
    margin-top: 2rem;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    position: relative;
}

.cta-card {
    background: white;
    border-radius: 16px;
    padding: 2.5rem;
    display: flex;
    align-items: center;
    gap: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 900px;
    margin: 0 auto;
    border-left: 4px solid #ff6b35;
    flex-direction: column;
    justify-content: center;
}

.cta-icon {
    flex-shrink: 0;
}

.lightbulb-icon {
    width: 60px;
    height: 60px;
    background: #fff3cd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    border: 3px solid #ffc107;
}

.cta-content {
    flex: 1;
}

.cta-title {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
    color: #2d3748;
    font-weight: 700;
}

.cta-description {
    font-size: 1.1rem;
    color: #6b7280;
    margin: 0;
    line-height: 1.5;
}

.cta-action {
    flex-shrink: 0;
}

.btn-cta {
    background: #ff6b35;
    color: white;
    padding: 1rem 2rem;
    border-radius: 8px;
    border: none;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.btn-cta:hover {
    background: #e55a2b;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

/* Footer */
.footer {
    background: #2d3748;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-logo-img {
    height: 40px;
    width: auto;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
}

.footer-column h4 {
    margin-bottom: 1rem;
    color: white;
}

.footer-column ul {
    list-style: none;
}

.footer-column li {
    margin-bottom: 0.5rem;
}

.footer-column a {
    color: #a0aec0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-column a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid #4a5568;
    padding-top: 1rem;
    text-align: center;
    color: #999;
}
.footer-bottom p {
    color:#999;
}
/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu, .nav-actions {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .offering-block {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .offering-block.reverse {
        direction: ltr;
    }

    .feature-highlights {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stakeholder-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .cta-card {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
        padding: 2rem;
    }

    .cta-title {
        font-size: 1.8rem;
    }
    
    .hero-actions {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .cta-title {
        font-size: 2rem;
    }
}
